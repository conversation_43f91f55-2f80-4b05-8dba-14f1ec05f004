"use client"

import * as React from "react"
import { usePathname } from "next/navigation"
import {
  PanelLeftOpen,
  PanelLeftClose,
  CircleDot,
} from "lucide-react"

import {
  DeviceIcon,
  FormulaIcon,
  MonitorIcon,
  HistoryIcon,
  UserIcon,
  SettingsIcon
} from "@/components/icons"

import { Button } from "@/components/ui/button"

import { NavMain } from "@/components/nav-main"
import { NavUser } from "@/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarRail,
  SidebarGroupLabel,
  useSidebar,
} from "@/components/ui/sidebar"



// This is sample data.
const data = {
  user: {
    name: "东方红汽轮机",
    email: "<EMAIL>",
    avatar: ""
  },

  navMain: [
    {
      title: "板卡设备",
      url: "/admin/devices",
      icon: DeviceIcon,
      isActive: true,
      items: [
        {
          title: "BTT_BOX_1",
          url: "/admin/devices/box_1",
        },
        {
          title: "BTT_BOX_2",
          url: "/admin/devices/box_2",
        },
        {
          title: "BTT_BOX_3",
          url: "/admin/devices/box_3",
        },
      ],
    },
    {
      title: "公式模版",
      url: "/admin/formula",
      icon: FormulaIcon,
    },
    {
      title: "实时观测",
      url: "/admin/monitor",
      icon: MonitorIcon,
      rightIcon: CircleDot,
      rightIconClassName: "text-primary/80 animate-pulse",
    },
    {
      title: "场景回放",
      url: "/admin/history",
      icon: HistoryIcon,
    },
    {
      title: "观测员",
      url: "/admin/users",
      icon: UserIcon,
    },
    {
      title: "工具",
      url: "/admin/utilities",
      icon: SettingsIcon,
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const {state, setOpen} = useSidebar()
  const pathname = usePathname()

  return (
    <Sidebar
      collapsible="icon"
      style={{
        "--sidebar-width": "16rem", // 展开时宽度 (256px)
      } as React.CSSProperties}
      {...props}
    >
      <SidebarContent className='bg-white'>
        { state == "expanded" && (
          <div className="flex items-center justify-between border-b" onClick={() => setOpen(false)}>
            <SidebarGroupLabel className='text-sm'> 项目管理员 </SidebarGroupLabel>
            <Button variant='ghost'>
              <PanelLeftClose />
            </Button>
          </div>
        )}

        { state == "collapsed" && (
          <Button variant='ghost' size='sm' className='border-b rounded-none shadow-none' onClick ={() => setOpen(true)} >
            <PanelLeftOpen />
          </Button>
        ) }

        <NavMain items={data.navMain} currentPath={pathname} />
      </SidebarContent>

      <SidebarFooter className='bg-white'>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
