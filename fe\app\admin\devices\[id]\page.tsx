'use client'

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON>oot<PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

import {
  Breadcrumb,
  BreadcrumbEllipsis,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { DeviceIcon } from '@/components/icons';

const channels = Array.from({length: 14}).fill(0).map((_, i) =>  {
  return {
    sn: `SN00${i}`,
    ai: "Text",
    sampleMode: "Mode1",
    threshold: "10",
    power: "5W",
    kangdoudongyuzhi: 100
    }})

export default function Page() {
  return (
    <div className="flex flex-col gap-4 p-4">
      <div id="breadcrumb" className="flex flex-row gap-4 justify-between items-center">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <DeviceIcon className="w-4 h-4" />
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              设备
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-meuted-foreground font-semibold">BTT_BOX_01</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <Button variant="outline" className=''>新建场景</Button>
      </div>

      <div className='flex flex-row gap-2 items-center'>
        <Button className=''>启动测试</Button>
        {/* <Button className='cursor-pointer rounded-none'>停止测试</Button> */}
        <Button variant="outline" className=''>打开激光</Button>
        {/* <Button className='cursor-pointer rounded-none'>关闭激光</Button> */}
        <Button variant="outline" className=''>批量编辑</Button>
        <Button variant="outline" className=''>保存参数</Button>
      </div>
      <div className="grid grid-cols-12 gap-4 h-[calc(100vh-220px)]">
        <div id="table" className="bg-background border rounded p-4 col-span-8 flex flex-col overflow-hidden">
          <CardTitle className="mb-4 flex-shrink-0">板卡通道</CardTitle>
          <div className="flex-1 overflow-auto">
            <Table>
              <TableCaption></TableCaption>
              <TableHeader className="sticky top-0 bg-background z-10">
                <TableRow className="bg-secondary font-semibold">
                  <TableHead className="font-semibold py-4">SN</TableHead>
                  <TableHead className="font-semibold py-4">AI</TableHead>
                  <TableHead className="font-semibold py-4">采样模式</TableHead>
                  <TableHead className="font-semibold py-4">触发阈值</TableHead>
                  <TableHead className="font-semibold py-4">激光功率</TableHead>
                  <TableHead className="font-semibold py-4">抗抖动阈值</TableHead>
                  <TableHead className="font-semibold py-4">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {channels.map((invoice) => (
                  <TableRow key={invoice.sn} className="h-10">
                    <TableCell className="font-semibold">{invoice.sn}</TableCell>
                    <TableCell className="font-semibold">{invoice.ai}</TableCell>
                    <TableCell>{invoice.sampleMode}</TableCell>
                    <TableCell>{invoice.threshold}</TableCell>
                    <TableCell>{invoice.power}</TableCell>
                    <TableCell>{invoice.kangdoudongyuzhi}</TableCell>
                    <TableCell></TableCell>
                  </TableRow>
                ))}
              </TableBody>
              <TableFooter>
                <TableRow>
                </TableRow>
              </TableFooter>
            </Table>
          </div>
        </div>

        <div id="logs" className="bg-background border rounded p-4 flex flex-col col-span-4 overflow-hidden">
          <CardTitle className="mb-4 flex-shrink-0">操作日志</CardTitle>
          <div id="content" className="bg-secondary font-mono text-sm p-2 flex-1 overflow-auto">
            content
          </div>
        </div>
      </div>

    </div>
  )
}
