import { AppSidebar } from "@/components/app-sidebar"
import { Separator } from "@/components/ui/separator"
import {
  SidebarInset,
  SidebarProvider,
} from "@/components/ui/sidebar"

import { Select, SelectContent, SelectItem, SelectTrigger } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Play, Square, Fullscreen } from 'lucide-react'
import Image from 'next/image'

export default function AdminLayout({
  children,
}: Readonly<{
    children: React.ReactNode;
  }>) {

  return (
    <SidebarProvider>
      <SidebarInset className='h-screen w-full overflow-hidden'>
        <header className="border flex h-16 shrink-0 items-center gap-2">
          <div className="w-full flex items-center justify-between gap-2 px-4">
            <div className="flex items-center gap-2">
              <Image src="/logo-2.svg" alt="Logo" width={28} height={28} />
              <div className="text-lg font-semibold text-gray-800">
                测试项目
              </div>
            </div>

            <div className='flex flex-row items-center gap-2'>
              <Select>
                <SelectTrigger className="!h-8">
                  当前配置版本：未命名
                </SelectTrigger>

                <SelectContent>
                  <SelectItem value="option1">Option 1</SelectItem>
                </SelectContent>
              </Select>

              <Button variant="outline" disabled size='sm'>保存版本</Button>

              <Separator
                orientation="vertical"
                className="data-[orientation=vertical]:h-4"
              />

              <Button size='sm' variant="default">
                <Play /> 开始采集
              </Button>

              <Button variant="outline" disabled size='sm'>
                <Square />
                停止采集
              </Button>

              <Button variant="outline" size='sm'>
                <Fullscreen />
              </Button>
            </div>
          </div>
        </header>

        <div className='flex w-full h-full overflow-hidden'>
          <AppSidebar />
          <main className="flex h-full w-full flex-col overflow-hidden bg-secondary p-2">
            { children }
          </main>
        </div>

      </SidebarInset>
    </SidebarProvider>
  );
}
