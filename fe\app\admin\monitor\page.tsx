'use client';

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

import { useRouter } from "next/navigation";

import { Plus, Trash2, Pencil } from "lucide-react";

export default function Page() {
  const router = useRouter();

  const gotoScene = () => {
    router.push('/admin/monitor/scene')
  }

  return (
    <div className="flex flex-col gap-4 p-4">
      <div className="flex flex-row gap-4 justify-between items-center h-full">
        <div>场景 - 场景列表</div>
        <Button className=''>新建场景</Button>
      </div>

      <div className="grid grid-cols-3 xl:grid-cols-5 gap-8">
        <Card className="cursor-pointer justify-center items-center flex hover:shadow-lg"
          onClick={gotoScene}
        >
          <Plus className="w-6 h-6 text-muted-foreground" />
          <h5 className="text-sm font-semibold">创建/复制场景</h5>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg">
          <CardHeader className=''>
            <CardTitle className='text-primary'>场景 4</CardTitle>
            <CardDescription>BTT单机风扇观测2025/03</CardDescription>
          </CardHeader>
          <CardContent>
            <p>用户行为分析之漏斗分析</p>
          </CardContent>
          <CardFooter className="flex flex-row items-center justify-between space-x-2">
            <div className='text-gray-400'>图表数量: 10</div>
            <div className="flex space-x-2">
              <Trash2 className="w-4 h-4" />
              <Pencil className='w-4 h-4' 
                onClick={gotoScene}
              />
            </div>
          </CardFooter>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg">
          <CardHeader>
            <CardTitle>场景 5</CardTitle>
            <CardDescription>BTT单机风扇观测2025/03</CardDescription>
          </CardHeader>
          <CardContent>
            <p>用户行为分析之漏斗分析</p>
          </CardContent>
          <CardFooter className="flex flex-row items-center justify-between space-x-2">
            <div className='text-gray-400'>图表数量: 10</div>
            <div className="flex space-x-2">
              <Trash2 className="w-4 h-4" />
              <Pencil className='w-4 h-4' 
                onClick={gotoScene}
              />
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
